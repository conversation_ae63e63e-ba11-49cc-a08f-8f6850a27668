import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 分页查询DTO
 */
export class PageQueryDTO {
  @Rule(RuleType.number().integer().min(1).default(1))
  page?: number = 1;

  @Rule(RuleType.number().integer().min(1).max(100).default(10))
  pageSize?: number = 10;

  @Rule(RuleType.string().optional().allow(null))
  keyword?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  regionId?: number;
}

/**
 * 分页响应DTO
 */
export class PageResponseDTO<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;

  constructor(list: T[], total: number, page: number, pageSize: number) {
    this.list = list;
    this.total = total;
    this.page = page;
    this.pageSize = pageSize;
    this.totalPages = Math.ceil(total / pageSize);
  }
}

/**
 * 地图数据查询DTO
 */
export class MapDataQueryDTO {
  @Rule(
    RuleType.string()
      .valid('mountain', 'water_system', 'historical_element')
      .optional()
      .allow(null)
  )
  type?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  regionId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  typeId?: number; // 类型字典ID，用于历史要素类型筛选
}

/**
 * 详情查询DTO
 */
export class DetailQueryDTO {
  @Rule(
    RuleType.string()
      .valid('mountain', 'water_system', 'historical_element')
      .required()
  )
  type: string;

  @Rule(RuleType.number().integer().required())
  id: number;
}

/**
 * 批量删除DTO
 */
export class BatchDeleteDTO {
  @Rule(RuleType.array().items(RuleType.number().integer()).min(1).required())
  ids: number[];

  @Rule(RuleType.boolean().optional().default(false))
  deletePhotos?: boolean = false;
}

/**
 * 统计查询DTO
 */
export class StatisticQueryDTO {
  @Rule(RuleType.number().integer().optional().allow(null))
  regionId?: number;

  @Rule(RuleType.date().optional().allow(null))
  startTime?: Date;

  @Rule(RuleType.date().optional().allow(null))
  endTime?: Date;
}
