-- 历史要素建造年份字段迁移脚本
-- 将 construction_time (DATE) 字段替换为 construction_year (INTEGER) 字段

USE zhi_hui_ying_jian;

-- 开始事务
START TRANSACTION;

-- 1. 添加新的建造年份字段
ALTER TABLE historical_element 
ADD COLUMN construction_year INT NULL 
COMMENT '建造年份（支持公元前，负数表示公元前）';

-- 2. 迁移现有数据：从 construction_time 提取年份到 construction_year
-- 注意：这里需要处理现有的日期数据
UPDATE historical_element 
SET construction_year = YEAR(construction_time) 
WHERE construction_time IS NOT NULL;

-- 3. 检查迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(construction_time) as records_with_old_field,
    COUNT(construction_year) as records_with_new_field
FROM historical_element;

-- 4. 显示迁移的数据示例
SELECT 
    id, 
    name, 
    construction_time, 
    construction_year 
FROM historical_element 
WHERE construction_time IS NOT NULL 
LIMIT 10;

-- 5. 删除旧的建造时间字段（注释掉，需要确认数据正确后再执行）
-- ALTER TABLE historical_element DROP COLUMN construction_time;

-- 提交事务
COMMIT;

-- 显示表结构
DESCRIBE historical_element;
