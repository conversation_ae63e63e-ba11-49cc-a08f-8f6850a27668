import { Provide, Inject } from '@midwayjs/core';
import { StatisticQueryDTO } from '../dto/common.dto';
import { MountainService } from './mountain.service';
import { WaterSystemService } from './water-system.service';
import { HistoricalElementService } from './historical-element.service';
import { RegionDictService } from './region-dict.service';
import { User } from '../entity/user.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RegionDict } from '../entity/region-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';

@Provide()
export class StatisticService {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  @Inject()
  regionDictService: RegionDictService;

  /**
   * 获取统计数据
   */
  async getStatisticData(query: StatisticQueryDTO) {
    const { regionId, startTime, endTime } = query;

    // 数量统计
    const mountainCount = await this.getMountainCount(regionId);
    const waterSystemCount = await this.getWaterSystemCount(regionId);
    const historicalElementCount = await this.getHistoricalElementCount(
      regionId,
      startTime,
      endTime
    );
    const userCount = await this.getUserCount();
    const typeDictCount = await this.getTypeDictCount();
    const regionDictCount = await this.getRegionDictCount();
    const relationshipDictCount = await this.getRelationshipDictCount();

    // 区域分布统计
    const regionStats = await this.getRegionStats();

    // 时间轴数据（历史要素按时间分布）
    const timelineData = await this.getTimelineData(
      regionId,
      startTime,
      endTime
    );

    return {
      counts: {
        mountain: mountainCount,
        waterSystem: waterSystemCount,
        historicalElement: historicalElementCount,
        user: userCount,
        typeDict: typeDictCount,
        regionDict: regionDictCount,
        relationshipDict: relationshipDictCount,
      },
      regionStats,
      timelineData,
    };
  }

  /**
   * 获取山塬数量统计
   */
  private async getMountainCount(regionId?: number): Promise<number> {
    const whereConditions: any = {};
    if (regionId && !isNaN(Number(regionId))) {
      whereConditions.regionDictId = Number(regionId);
    }

    const result = await this.mountainService.findAll({
      query: whereConditions,
    });

    // 未开启分页时 total 不返回，这里用 list.length 统计
    return Array.isArray(result.list) ? result.list.length : 0;
  }

  /**
   * 获取水系数量统计
   */
  private async getWaterSystemCount(regionId?: number): Promise<number> {
    const whereConditions: any = {};
    if (regionId && !isNaN(Number(regionId))) {
      whereConditions.regionDictId = Number(regionId);
    }

    const result = await this.waterSystemService.findAll({
      query: whereConditions,
    });

    // 未开启分页时 total 不返回，这里用 list.length 统计
    return Array.isArray(result.list) ? result.list.length : 0;
  }

  /**
   * 获取历史要素数量统计
   */
  private async getHistoricalElementCount(
    regionId?: number,
    startYear?: number,
    endYear?: number
  ): Promise<number> {
    const whereConditions: any = {};

    if (regionId && !isNaN(Number(regionId))) {
      whereConditions.regionDictId = Number(regionId);
    }

    if (startYear !== undefined) {
      whereConditions.constructionYear = { [Symbol.for('gte')]: startYear };
    }

    if (endYear !== undefined) {
      if (whereConditions.constructionYear) {
        whereConditions.constructionYear[Symbol.for('lte')] = endYear;
      } else {
        whereConditions.constructionYear = { [Symbol.for('lte')]: endYear };
      }
    }

    const result = await this.historicalElementService.findAll({
      query: whereConditions,
    });

    // 由于没有分页参数，result.total 为 undefined，需要手动计算总数
    return result.list.length;
  }

  /**
   * 获取区域分布统计
   */
  private async getRegionStats() {
    const regions = await this.regionDictService.getRegionDictList();
    const stats = [];

    for (const region of regions) {
      const mountainCount = await this.getMountainCount(region.id);
      const waterSystemCount = await this.getWaterSystemCount(region.id);
      const historicalElementCount = await this.getHistoricalElementCount(
        region.id
      );

      stats.push({
        region: region.regionName,
        regionId: region.id,
        mountainCount,
        waterSystemCount,
        historicalElementCount,
        total: mountainCount + waterSystemCount + historicalElementCount,
      });
    }

    return stats;
  }

  /**
   * 获取时间轴数据
   */
  private async getTimelineData(
    regionId?: number,
    startTime?: Date,
    endTime?: Date
  ) {
    return await this.historicalElementService.getTimelineData(regionId);
  }

  /**
   * 获取用户数量
   */
  private async getUserCount(): Promise<number> {
    return await User.count();
  }

  /**
   * 获取类型字典数量
   */
  private async getTypeDictCount(): Promise<number> {
    return await TypeDict.count();
  }

  /**
   * 获取区域字典数量
   */
  private async getRegionDictCount(): Promise<number> {
    return await RegionDict.count();
  }

  /**
   * 获取关系字典数量
   */
  private async getRelationshipDictCount(): Promise<number> {
    return await RelationshipDict.count();
  }

  /**
   * 获取综合统计报告
   */
  async getComprehensiveReport(regionId?: number) {
    const basicStats = await this.getStatisticData({ regionId });

    // 获取各类型详细统计
    const mountainStats = await this.mountainService.getStatistics(regionId);
    const waterSystemStats = await this.waterSystemService.getStatistics(
      regionId
    );
    const historicalElementStats =
      await this.historicalElementService.getStatistics(regionId);

    return {
      basic: basicStats,
      detailed: {
        mountains: mountainStats,
        waterSystems: waterSystemStats,
        historicalElements: historicalElementStats,
      },
      summary: {
        totalEntities:
          basicStats.counts.mountain +
          basicStats.counts.waterSystem +
          basicStats.counts.historicalElement,
        regionCoverage: basicStats.regionStats.length,
        timeSpan: this.calculateTimeSpan(basicStats.timelineData),
      },
    };
  }

  /**
   * 计算时间跨度
   */
  private calculateTimeSpan(timelineData: any[]): {
    earliest: number | null;
    latest: number | null;
    span: number;
  } {
    if (!timelineData || timelineData.length === 0) {
      return { earliest: null, latest: null, span: 0 };
    }

    const years = timelineData.map(item => item.year).sort((a, b) => a - b);
    const earliest = years[0];
    const latest = years[years.length - 1];

    return {
      earliest,
      latest,
      span: latest - earliest,
    };
  }
}
