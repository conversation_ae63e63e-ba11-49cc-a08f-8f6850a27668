import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
  Files,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { HistoricalElementService } from '../../service/historical-element.service';
import { ExcelService } from '../../service/excel.service';
import { PageQueryDTO, BatchDeleteDTO } from '../../dto/common.dto';
import {
  CreateHistoricalElementDTO,
  UpdateHistoricalElementDTO,
} from '../../dto/entity.dto';
import { promises as fs } from 'fs';
import { Context as KoaContext } from '@midwayjs/koa';

/**
 * 历史要素管理控制器
 */
@Controller('/admin/historical-element', {
  middleware: [JwtMiddleware, AuthMiddleware],
})
export class AdminHistoricalElementController {
  @Inject()
  historicalElementService: HistoricalElementService;

  @Inject()
  excelService: ExcelService;

  @Inject()
  ctx: KoaContext;

  /**
   * 创建历史要素
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateHistoricalElementDTO) {
    const data = await this.historicalElementService.createHistoricalElement(
      createDto
    );
    return data;
  }

  /**
   * 更新历史要素
   */
  @Put('/:id')
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateHistoricalElementDTO
  ) {
    const data = await this.historicalElementService.updateHistoricalElement(
      id,
      updateDto
    );
    return data;
  }

  /**
   * 删除历史要素
   */
  @Del('/:id')
  async delete(
    @Param('id') id: number,
    @Query('deletePhotos') deletePhotos?: string
  ) {
    // 将字符串参数转换为布尔值，默认为false（保留照片，外键设置为NULL）
    const shouldDeletePhotos = deletePhotos === 'true';
    await this.historicalElementService.deleteHistoricalElement(
      id,
      shouldDeletePhotos
    );
    return {
      message: '删除成功',
      deletedPhotos: shouldDeletePhotos,
    };
  }

  /**
   * 批量删除历史要素
   */
  @Del('/batch-delete')
  @Validate()
  async batchDelete(@Body() batchDeleteDto: BatchDeleteDTO) {
    await this.historicalElementService.batchDeleteHistoricalElements(
      batchDeleteDto.ids,
      batchDeleteDto.deletePhotos
    );
    return {
      message: '批量删除成功',
      deletedCount: batchDeleteDto.ids.length,
      deletedPhotos: batchDeleteDto.deletePhotos,
    };
  }

  /**
   * 获取历史要素列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO & { typeId?: number }) {
    const data = await this.historicalElementService.findList(query);
    return data;
  }

  /**
   * 获取历史要素详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    // 验证ID参数
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的历史要素ID');
    }
    const data = await this.historicalElementService.findById(Number(id));
    if (!data) {
      throw new Error('历史要素不存在');
    }
    return data;
  }

  /**
   * 批量导入历史要素
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { elements: CreateHistoricalElementDTO[] }) {
    await this.historicalElementService.batchImportElements(data.elements);
    return { message: '批量导入成功' };
  }

  /**
   * 获取历史要素统计
   */
  @Get('/statistics/overview')
  async getStatistics(
    @Query('regionId') regionId?: number,
    @Query('typeId') typeId?: number
  ) {
    const data = await this.historicalElementService.getStatistics(
      regionId,
      typeId
    );
    return data;
  }

  /**
   * 根据类型获取历史要素
   */
  @Get('/by-type/:typeId')
  async getByType(@Param('typeId') typeId: number) {
    const data = await this.historicalElementService.findByType(typeId);
    return data;
  }

  /**
   * 根据区域获取历史要素
   */
  @Get('/by-region/:regionId')
  async getByRegion(@Param('regionId') regionId: number) {
    const data = await this.historicalElementService.findByRegion(regionId);
    return data;
  }

  /**
   * 根据建造时间范围查询
   */
  @Get('/by-construction-time')
  async getByConstructionTime(
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    const start = startTime ? new Date(startTime) : undefined;
    const end = endTime ? new Date(endTime) : undefined;
    const data = await this.historicalElementService.findByConstructionTime(
      start,
      end
    );
    return data;
  }

  /**
   * 获取时间轴数据
   */
  @Get('/timeline')
  async getTimelineData(@Query('regionId') regionId?: number) {
    const data = await this.historicalElementService.getTimelineData(regionId);
    return data;
  }

  /**
   * 获取导入模板
   */
  @Get('/template/download')
  async downloadTemplate() {
    console.log('🏛️ 获取历史要素导入模板');

    try {
      const buffer =
        await this.excelService.generateHistoricalElementTemplate();
      return {
        downloadUrl:
          '/public/templates/historical_element_import_template.xlsx',
        filename: '历史要素导入模板.xlsx',
        description: '点击链接下载Excel导入模板，包含字段说明和示例数据',
        buffer: buffer.toString('base64'),
      };
    } catch (error) {
      console.error('🏛️ 获取导入模板失败:', error);
      throw new Error(`获取模板失败: ${error.message}`);
    }
  }

  /**
   * Excel文件导入
   */
  @Post('/import/excel', { middleware: [UploadMiddleware] })
  @Validate()
  async importFromExcel(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    // 验证文件大小（限制为10MB）
    const fileSize = await this.getFileSize(file);
    if (fileSize > 10 * 1024 * 1024) {
      throw new Error('文件大小不能超过10MB');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseHistoricalElementExcel(
        file.data as string
      );

      if (!parseResult.success) {
        return {
          success: false,
          message: '文件解析失败',
          errors: parseResult.errors,
          totalRows: parseResult.totalRows || 0,
          validRows: 0,
        };
      }

      // 如果解析成功但没有有效数据
      if (!parseResult.data || parseResult.data.length === 0) {
        return {
          success: false,
          message: '文件中没有有效的数据行',
          totalRows: parseResult.totalRows || 0,
          validRows: 0,
        };
      }

      // 批量导入数据
      await this.historicalElementService.batchImportElements(parseResult.data);

      return {
        success: true,
        message: '导入成功',
        totalRows: parseResult.totalRows,
        validRows: parseResult.validRows,
        importedCount: parseResult.data.length,
      };
    } catch (error) {
      throw new Error(`导入失败: ${error.message}`);
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 验证Excel文件预览（不实际导入）
   */
  @Post('/import/preview', { middleware: [UploadMiddleware] })
  @Validate()
  async previewImport(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseHistoricalElementExcel(
        file.data as string
      );

      return {
        success: parseResult.success,
        message: parseResult.success ? '文件格式正确' : '文件存在错误',
        errors: parseResult.errors,
        totalRows: parseResult.totalRows || 0,
        validRows: parseResult.validRows || 0,
        preview: parseResult.data?.slice(0, 5), // 只返回前5条数据作为预览
      };
    } catch (error) {
      return {
        success: false,
        message: `文件解析失败: ${error.message}`,
        totalRows: 0,
        validRows: 0,
      };
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 验证是否为Excel文件
   */
  private isExcelFile(filename: string): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return extension === 'xlsx' || extension === 'xls';
  }

  /**
   * 获取文件大小
   */
  private async getFileSize(file: UploadFileInfo): Promise<number> {
    if (typeof file.data === 'string') {
      const stats = await fs.stat(file.data);
      return stats.size;
    }
    return 0;
  }
}
