/**
 * 测试建造年份API功能的脚本
 */

const http = require('http');

const BASE_URL = 'http://localhost:7001';

// 测试数据
const testData = {
  // 公元后年份
  modernElement: {
    name: '测试现代建筑',
    code: 'TEST_MODERN_001',
    constructionYear: 1980,
    locationDescription: '测试位置',
    regionDictId: 1
  },
  // 公元前年份
  ancientElement: {
    name: '测试古代建筑',
    code: 'TEST_ANCIENT_001', 
    constructionYear: -221,
    locationDescription: '测试古代位置',
    regionDictId: 1
  },
  // 无效年份（应该失败）
  invalidElement: {
    name: '测试无效年份',
    code: 'TEST_INVALID_001',
    constructionYear: 0, // 公元0年不存在
    locationDescription: '测试位置',
    regionDictId: 1
  }
};

async function testAPI() {
  console.log('🧪 开始测试建造年份API功能...\n');

  try {
    // 1. 测试创建历史要素（现代）
    console.log('1️⃣ 测试创建现代历史要素...');
    try {
      const response1 = await axios.post(`${BASE_URL}/openapi/historical-element`, testData.modernElement);
      console.log('✅ 现代历史要素创建成功:', response1.data);
    } catch (error) {
      console.log('❌ 现代历史要素创建失败:', error.response?.data || error.message);
    }

    // 2. 测试创建历史要素（古代）
    console.log('\n2️⃣ 测试创建古代历史要素...');
    try {
      const response2 = await axios.post(`${BASE_URL}/openapi/historical-element`, testData.ancientElement);
      console.log('✅ 古代历史要素创建成功:', response2.data);
    } catch (error) {
      console.log('❌ 古代历史要素创建失败:', error.response?.data || error.message);
    }

    // 3. 测试创建无效年份（应该失败）
    console.log('\n3️⃣ 测试创建无效年份历史要素（应该失败）...');
    try {
      const response3 = await axios.post(`${BASE_URL}/openapi/historical-element`, testData.invalidElement);
      console.log('❌ 无效年份应该失败但成功了:', response3.data);
    } catch (error) {
      console.log('✅ 无效年份正确被拒绝:', error.response?.data?.message || error.message);
    }

    // 4. 测试按年份范围查询
    console.log('\n4️⃣ 测试按年份范围查询...');
    try {
      const response4 = await axios.get(`${BASE_URL}/admin/historical-element/by-construction-year?startYear=-300&endYear=2000`);
      console.log('✅ 年份范围查询成功，找到', response4.data?.length || 0, '条记录');
      if (response4.data && response4.data.length > 0) {
        console.log('   示例记录:', response4.data.slice(0, 2).map(item => ({
          name: item.name,
          constructionYear: item.constructionYear
        })));
      }
    } catch (error) {
      console.log('❌ 年份范围查询失败:', error.response?.data || error.message);
    }

    // 5. 测试获取时间轴数据
    console.log('\n5️⃣ 测试获取时间轴数据...');
    try {
      const response5 = await axios.get(`${BASE_URL}/openapi/statistics/timeline`);
      console.log('✅ 时间轴数据获取成功，包含', response5.data?.length || 0, '个年份');
      if (response5.data && response5.data.length > 0) {
        console.log('   年份范围:', {
          earliest: Math.min(...response5.data.map(item => item.year)),
          latest: Math.max(...response5.data.map(item => item.year))
        });
      }
    } catch (error) {
      console.log('❌ 时间轴数据获取失败:', error.response?.data || error.message);
    }

    // 6. 测试统计数据
    console.log('\n6️⃣ 测试统计数据...');
    try {
      const response6 = await axios.get(`${BASE_URL}/openapi/statistics/basic`);
      console.log('✅ 统计数据获取成功:', {
        历史要素总数: response6.data?.counts?.historicalElement || 0,
        时间轴数据点: response6.data?.timelineData?.length || 0
      });
    } catch (error) {
      console.log('❌ 统计数据获取失败:', error.response?.data || error.message);
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testAPI().catch(console.error);
