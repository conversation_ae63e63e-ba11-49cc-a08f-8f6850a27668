/**
 * 手动执行数据库迁移脚本
 * 用法: node scripts/run-migration.js [migration-file-name]
 */

const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');
const fs = require('fs');

// 数据库配置（从配置文件读取）
const dbConfig = {
  dialect: 'mysql',
  host: '***********',
  port: 3306,
  username: 'remote_user',
  password: 'Aa@123456',
  database: 'zhi_hui_ying_jian',
  timezone: '+08:00',
  logging: console.log, // 显示SQL日志
};

async function runMigration() {
  const migrationName = process.argv[2];
  
  if (!migrationName) {
    console.log('请指定要执行的迁移文件名');
    console.log('用法: node scripts/run-migration.js 20241212000000-modify-historical-element-construction-time');
    process.exit(1);
  }

  // 创建数据库连接
  const sequelize = new Sequelize(dbConfig);

  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 构建迁移文件路径
    const migrationPath = path.join(__dirname, '..', 'src', 'migration', `${migrationName}.ts`);
    
    if (!fs.existsSync(migrationPath)) {
      console.error(`❌ 迁移文件不存在: ${migrationPath}`);
      process.exit(1);
    }

    console.log(`📄 加载迁移文件: ${migrationPath}`);

    // 动态导入迁移文件
    const migration = require(migrationPath);
    const migrationModule = migration.default || migration;

    if (!migrationModule.up) {
      console.error('❌ 迁移文件格式错误：缺少 up 方法');
      process.exit(1);
    }

    // 创建QueryInterface
    const queryInterface = sequelize.getQueryInterface();

    console.log('🚀 开始执行迁移...');
    
    // 执行迁移
    await migrationModule.up(queryInterface, Sequelize);
    
    console.log('✅ 迁移执行成功！');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行迁移
runMigration().catch(console.error);
