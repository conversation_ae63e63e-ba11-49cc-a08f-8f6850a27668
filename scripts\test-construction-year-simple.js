/**
 * 简单测试建造年份功能
 */

console.log('🧪 测试建造年份功能...\n');

// 测试年份格式化和解析
function testYearFormatting() {
  console.log('=== 测试年份格式化功能 ===');
  
  function formatYear(year) {
    if (year < 0) {
      return `公元前${Math.abs(year)}年`;
    } else {
      return `公元${year}年`;
    }
  }

  const testCases = [
    { year: 652, expected: '公元652年' },
    { year: -221, expected: '公元前221年' },
    { year: 1, expected: '公元1年' },
    { year: -1, expected: '公元前1年' },
  ];

  testCases.forEach(({ year, expected }) => {
    const result = formatYear(year);
    const status = result === expected ? '✅' : '❌';
    console.log(`${status} ${year} -> ${result}`);
  });
}

function testYearValidation() {
  console.log('\n=== 测试年份验证功能 ===');
  
  function validateYear(year) {
    const currentYear = 2024;
    
    if (year > currentYear) {
      return { valid: false, message: '建造年份不能晚于当前年份' };
    }
    
    if (year < -5000) {
      return { valid: false, message: '建造年份不能早于公元前5000年' };
    }
    
    if (year === 0) {
      return { valid: false, message: '年份不能为0，请使用公元前1年（-1）或公元1年（1）' };
    }
    
    return { valid: true, message: '年份有效' };
  }

  const testCases = [
    { year: 652, shouldBeValid: true },
    { year: -221, shouldBeValid: true },
    { year: 2025, shouldBeValid: false },
    { year: 0, shouldBeValid: false },
    { year: -5001, shouldBeValid: false },
  ];

  testCases.forEach(({ year, shouldBeValid }) => {
    const result = validateYear(year);
    const status = result.valid === shouldBeValid ? '✅' : '❌';
    console.log(`${status} ${year} -> ${result.valid ? '有效' : '无效'}: ${result.message}`);
  });
}

// 测试API可用性
async function testAPIAvailability() {
  console.log('\n=== 测试API可用性 ===');
  
  const testUrls = [
    'http://localhost:7001/openapi/historical-element/all',
    'http://localhost:7001/openapi/statistics/timeline',
    'http://localhost:7001/openapi/statistics/basic'
  ];

  for (const url of testUrls) {
    try {
      const response = await fetch(url);
      const status = response.ok ? '✅' : '❌';
      console.log(`${status} ${url} -> ${response.status}`);
    } catch (error) {
      console.log(`❌ ${url} -> 连接失败: ${error.message}`);
    }
  }
}

// 运行测试
async function runTests() {
  testYearFormatting();
  testYearValidation();
  
  // 检查是否有fetch API（Node.js 18+）
  if (typeof fetch !== 'undefined') {
    await testAPIAvailability();
  } else {
    console.log('\n=== API测试跳过 ===');
    console.log('ℹ️ 需要Node.js 18+或手动测试API');
  }
  
  console.log('\n🎉 测试完成！');
  
  console.log('\n📋 手动测试建议：');
  console.log('1. 访问 http://localhost:7001/openapi/historical-element/all');
  console.log('2. 访问 http://localhost:7001/openapi/statistics/timeline');
  console.log('3. 使用Postman或curl测试创建历史要素API');
}

runTests().catch(console.error);
