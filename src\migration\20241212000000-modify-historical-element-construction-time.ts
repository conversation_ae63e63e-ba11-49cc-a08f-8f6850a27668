import { QueryInterface, DataTypes } from 'sequelize';

export default {
  async up(queryInterface: QueryInterface): Promise<void> {
    // 1. 添加新的建造年份字段
    await queryInterface.addColumn('historical_element', 'construction_year', {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '建造年份（支持公元前，负数表示公元前）',
    });

    // 2. 迁移现有数据：从 construction_time 提取年份到 construction_year
    // 注意：这里需要处理现有的日期数据
    await queryInterface.sequelize.query(`
      UPDATE historical_element 
      SET construction_year = YEAR(construction_time) 
      WHERE construction_time IS NOT NULL
    `);

    // 3. 删除旧的建造时间字段
    await queryInterface.removeColumn('historical_element', 'construction_time');
  },

  async down(queryInterface: QueryInterface): Promise<void> {
    // 回滚：重新添加 construction_time 字段
    await queryInterface.addColumn('historical_element', 'construction_time', {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '建筑时间',
    });

    // 尝试从 construction_year 恢复数据（只能恢复年份，月日设为1月1日）
    await queryInterface.sequelize.query(`
      UPDATE historical_element 
      SET construction_time = CONCAT(
        CASE 
          WHEN construction_year < 0 THEN CONCAT(ABS(construction_year), '-01-01 00:00:00')
          ELSE CONCAT(construction_year, '-01-01 00:00:00')
        END
      )
      WHERE construction_year IS NOT NULL
    `);

    // 删除 construction_year 字段
    await queryInterface.removeColumn('historical_element', 'construction_year');
  },
};
