# 历史要素建造年份扩展功能

## 概述

本次更新扩展了历史要素的建造时间属性，从原来的完整日期时间格式改为只需要输入年份，并且支持公元前的年份表示。

## 主要变更

### 1. 数据库结构变更

- **原字段**: `construction_time` (DATE类型)
- **新字段**: `construction_year` (INTEGER类型)
- **支持范围**: 公元前5000年 到 当前年份
- **公元前表示**: 负数表示公元前年份（如：-221 表示公元前221年）

### 2. 数据迁移

创建了数据库迁移文件 `20241212000000-modify-historical-element-construction-time.ts`：
- 自动将现有的 `construction_time` 数据转换为 `construction_year`
- 提供回滚机制（虽然会丢失月日信息）

### 3. API接口变更

#### 创建/更新历史要素
```json
{
  "name": "大雁塔",
  "code": "DYT001",
  "constructionYear": 652,  // 新字段：公元652年
  // 其他字段...
}
```

#### 公元前年份示例
```json
{
  "name": "秦始皇陵",
  "code": "QSHL001", 
  "constructionYear": -221,  // 公元前221年
  // 其他字段...
}
```

#### 查询接口变更
- **原接口**: `/admin/historical-element/by-construction-time`
- **新接口**: `/admin/historical-element/by-construction-year`

```bash
# 查询公元前3世纪到公元7世纪的历史要素
GET /admin/historical-element/by-construction-year?startYear=-300&endYear=700
```

### 4. 验证规则

- **年份范围**: 公元前5000年 至 当前年份
- **特殊限制**: 不允许年份为0（历史上没有公元0年）
- **格式要求**: 整数，负数表示公元前

### 5. 辅助功能

#### 年份格式化
```javascript
formatYear(652)   // 返回: "公元652年"
formatYear(-221)  // 返回: "公元前221年"
```

#### 年份解析
```javascript
parseYear("公元前221年")  // 返回: -221
parseYear("公元652年")    // 返回: 652
parseYear("-221")        // 返回: -221
parseYear("652")         // 返回: 652
```

## 使用示例

### 1. 创建历史要素

```bash
curl -X POST "http://localhost:7001/admin/historical-element" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "兵马俑",
    "code": "BMY001",
    "constructionYear": -210,
    "locationDescription": "位于西安市临潼区",
    "historicalRecords": "秦始皇帝陵的一部分，约建于公元前210年",
    "regionDictId": 1
  }'
```

### 2. 按年份范围查询

```bash
# 查询秦朝时期的历史要素（公元前221年-公元前206年）
curl -X GET "http://localhost:7001/admin/historical-element/by-construction-year?startYear=-221&endYear=-206" \
  -H "Authorization: Bearer {token}"
```

### 3. Excel导入格式

| 名称 | 编号 | 建造年份 | 其他字段... |
|------|------|----------|-------------|
| 大雁塔 | DYT001 | 652 | ... |
| 兵马俑 | BMY001 | -210 | ... |

## 兼容性说明

### 数据迁移
- 现有数据会自动从 `construction_time` 提取年份到 `construction_year`
- 原有的月日信息会丢失（这是预期行为）

### API兼容性
- 旧的 `/by-construction-time` 接口已被 `/by-construction-year` 替代
- 前端需要相应更新以使用新的字段名和接口

### 文档更新
- API文档已更新以反映新的字段结构
- Excel导入模板已更新

## 测试

运行测试脚本验证功能：
```bash
node scripts/test-construction-year.js
```

测试覆盖：
- 年份格式化功能
- 年份解析功能  
- 年份验证规则

## 注意事项

1. **历史准确性**: 公元0年在历史上不存在，系统会拒绝年份为0的输入
2. **数据精度**: 只保留年份信息，不再支持具体的月日
3. **负数表示**: 负数专门用于表示公元前年份，请确保正确使用
4. **范围限制**: 最早支持到公元前5000年，这已经覆盖了人类文明的主要历史时期

## 后续计划

- 考虑添加朝代/时期的自动识别功能
- 优化时间轴显示，更好地处理公元前年份
- 添加年份的多语言显示支持
