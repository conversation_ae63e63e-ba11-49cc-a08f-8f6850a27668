/**
 * 测试建造年份功能的脚本
 */

// 测试年份格式化和解析功能
function testYearFormatting() {
  console.log('=== 测试年份格式化功能 ===');
  
  const testCases = [
    { year: 652, expected: '公元652年' },
    { year: -221, expected: '公元前221年' },
    { year: 1, expected: '公元1年' },
    { year: -1, expected: '公元前1年' },
    { year: 2024, expected: '公元2024年' },
    { year: -3000, expected: '公元前3000年' }
  ];

  // 模拟格式化函数
  function formatYear(year) {
    if (year < 0) {
      return `公元前${Math.abs(year)}年`;
    } else {
      return `公元${year}年`;
    }
  }

  testCases.forEach(({ year, expected }) => {
    const result = formatYear(year);
    const status = result === expected ? '✓' : '✗';
    console.log(`${status} ${year} -> ${result} (期望: ${expected})`);
  });
}

function testYearParsing() {
  console.log('\n=== 测试年份解析功能 ===');
  
  const testCases = [
    { input: '公元前221年', expected: -221 },
    { input: '公元652年', expected: 652 },
    { input: '公元前1年', expected: -1 },
    { input: '公元1年', expected: 1 },
    { input: '-221', expected: -221 },
    { input: '652', expected: 652 },
    { input: '公元前3000', expected: -3000 },
    { input: '公元2024', expected: 2024 },
    { input: 'invalid', expected: null },
    { input: '', expected: null }
  ];

  // 模拟解析函数
  function parseYear(yearStr) {
    if (!yearStr || typeof yearStr !== 'string') {
      return null;
    }

    const trimmed = yearStr.trim();
    
    // 处理纯数字格式
    const numericMatch = trimmed.match(/^-?\d+$/);
    if (numericMatch) {
      return parseInt(trimmed, 10);
    }

    // 处理中文格式
    const bcMatch = trimmed.match(/^公元前(\d+)年?$/);
    if (bcMatch) {
      return -parseInt(bcMatch[1], 10);
    }

    const adMatch = trimmed.match(/^公元(\d+)年?$/);
    if (adMatch) {
      return parseInt(adMatch[1], 10);
    }

    return null;
  }

  testCases.forEach(({ input, expected }) => {
    const result = parseYear(input);
    const status = result === expected ? '✓' : '✗';
    console.log(`${status} "${input}" -> ${result} (期望: ${expected})`);
  });
}

function testYearValidation() {
  console.log('\n=== 测试年份验证功能 ===');
  
  const testCases = [
    { year: 2024, valid: true, reason: '当前年份' },
    { year: 652, valid: true, reason: '历史年份' },
    { year: -221, valid: true, reason: '公元前年份' },
    { year: 2025, valid: false, reason: '未来年份' },
    { year: 0, valid: false, reason: '公元0年不存在' },
    { year: -5001, valid: false, reason: '过于久远' },
    { year: -3000, valid: true, reason: '合理的古代年份' }
  ];

  // 模拟验证函数
  function validateYear(year) {
    const currentYear = 2024; // 固定当前年份用于测试

    if (year > currentYear) {
      return { valid: false, message: '建造年份不能晚于当前年份' };
    }

    if (year < -5000) {
      return { valid: false, message: '建造年份不能早于公元前5000年' };
    }

    if (year === 0) {
      return { valid: false, message: '年份不能为0，请使用公元前1年（-1）或公元1年（1）' };
    }

    return { valid: true, message: '年份有效' };
  }

  testCases.forEach(({ year, valid, reason }) => {
    const result = validateYear(year);
    const status = result.valid === valid ? '✓' : '✗';
    console.log(`${status} ${year} (${reason}) -> ${result.valid ? '有效' : '无效'}: ${result.message}`);
  });
}

// 运行测试
console.log('开始测试建造年份功能...\n');
testYearFormatting();
testYearParsing();
testYearValidation();
console.log('\n测试完成！');
