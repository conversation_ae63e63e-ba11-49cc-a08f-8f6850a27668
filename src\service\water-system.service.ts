import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { Op } from 'sequelize';
import { WaterSystem } from '../entity/water-system.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import { CreateWaterSystemDTO, UpdateWaterSystemDTO } from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { PhotoService } from './photo.service';
import { BaseService } from '../common/BaseService';
import { RegionDict } from '../entity';

@Provide()
export class WaterSystemService extends BaseService<WaterSystem> {
  @Inject()
  cacheService: CacheService;

  @Inject()
  photoService: PhotoService;

  constructor() {
    super('水系');
  }

  protected getModel(): ModelCtor<WaterSystem> {
    return WaterSystem;
  }

  /**
   * 创建水系（业务逻辑封装）
   */
  async createWaterSystem(
    createDto: CreateWaterSystemDTO
  ): Promise<WaterSystem> {
    await this.validateWaterSystemData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新水系（业务逻辑封装）
   */
  async updateWaterSystem(
    id: number,
    updateDto: UpdateWaterSystemDTO
  ): Promise<WaterSystem> {
    await this.validateWaterSystemData(updateDto, id);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as WaterSystem;
  }

  /**
   * 删除水系（业务逻辑封装）
   * @param id 水系ID
   * @param deletePhotos 是否同时删除关联的照片，默认为false（外键设置为SET NULL）
   */
  async deleteWaterSystem(id: number, deletePhotos = false): Promise<void> {
    // 检查水系是否存在
    const waterSystem = await this.findById(id);
    if (!waterSystem) {
      throw new Error('水系不存在');
    }

    try {
      // 如果用户指定要删除照片，则先删除关联的照片记录
      if (deletePhotos) {
        await this.deleteRelatedPhotos(id);
      }

      // 删除水系本身
      // 如果deletePhotos为false，外键会被设置为NULL（需要数据库层面配置ON DELETE SET NULL）
      await this.delete({ id });
    } catch (error) {
      throw new Error(`删除水系失败: ${error.message}`);
    }
  }

  /**
   * 删除关联的照片记录
   * @param waterSystemId 水系ID
   */
  private async deleteRelatedPhotos(waterSystemId: number): Promise<void> {
    try {
      // 获取关联的照片记录
      const photos = await this.photoService.findAll({
        query: { waterSystemId },
      });

      // 删除照片记录
      for (const photo of photos.list) {
        await this.photoService.delete({ id: photo.id });
      }
    } catch (error) {
      throw new Error(`删除关联照片失败: ${error.message}`);
    }
  }

  /**
   * 批量删除水系
   * @param ids 水系ID数组
   * @param deletePhotos 是否同时删除关联的照片，默认为false
   */
  async batchDeleteWaterSystems(
    ids: number[],
    deletePhotos = false
  ): Promise<void> {
    if (!ids || ids.length === 0) {
      throw new Error('请提供要删除的水系ID');
    }

    // 检查所有水系是否存在
    const waterSystems = await this.findAll({
      query: { id: { [Op.in]: ids } },
    });

    const existingIds = waterSystems.list.map(w => w.id);
    const notFoundIds = ids.filter(id => !existingIds.includes(id));

    if (notFoundIds.length > 0) {
      throw new Error(`以下水系不存在: ${notFoundIds.join(', ')}`);
    }

    try {
      // 如果用户指定要删除照片，则先删除关联的照片记录
      if (deletePhotos) {
        for (const id of ids) {
          await this.deleteRelatedPhotos(id);
        }
      }

      // 批量删除水系
      await this.delete({ id: { [Op.in]: ids } });
    } catch (error) {
      throw new Error(`批量删除水系失败: ${error.message}`);
    }
  }

  /**
   * 分页查询水系列表
   */
  async findList(
    query: PageQueryDTO & { regionId?: number }
  ): Promise<PageResponseDTO<WaterSystem>> {
    const { page, pageSize, keyword, regionId } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: RegionDict,
          as: 'regionDict',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据编号查找水系
   */
  async findByCode(code: string): Promise<WaterSystem | null> {
    const result = await this.findAll({
      query: { code } as any,
      limit: 1,
    });
    return result.list.length > 0 ? result.list[0] : null;
  }

  /**
   * 根据区域获取水系列表
   */
  async findByRegion(regionId: number): Promise<WaterSystem[]> {
    // 验证regionId参数
    if (!regionId || isNaN(Number(regionId))) {
      throw new Error('无效的区域ID');
    }

    const result = await this.findAll({
      query: { regionDictId: Number(regionId) },
      include: [
        {
          model: RegionDict,
          as: 'regionDict',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });
    return result.list;
  }

  /**
   * 获取水系统计信息
   */
  async getStatistics(regionId?: number): Promise<{
    total: number;
    byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    byLengthArea: Array<{ category: string; count: number }>;
  }> {
    const whereConditions: any = {};

    if (regionId && !isNaN(Number(regionId))) {
      whereConditions.regionDictId = Number(regionId);
    }

    // 获取所有水系数据，包含关联的区域信息
    const result = await this.findAll({
      query: whereConditions,
      include: [{ model: RegionDict, as: 'regionDict' }],
    });
    const waterSystems = result.list;
    const total = waterSystems.length;

    // 按区域统计
    const regionMap = new Map<
      number,
      { regionId: number; regionName: string; count: number }
    >();

    // 按长度/面积类型统计
    const lengthAreaCategories = [
      { category: '河流', count: 0 },
      { category: '湖泊', count: 0 },
      { category: '水库', count: 0 },
      { category: '其他', count: 0 },
    ];

    waterSystems.forEach(waterSystem => {
      // 区域统计
      if (waterSystem.regionDict) {
        const regionId = waterSystem.regionDict.id;
        const regionName = waterSystem.regionDict.regionName;
        if (!regionMap.has(regionId)) {
          regionMap.set(regionId, { regionId, regionName, count: 0 });
        }
        regionMap.get(regionId)!.count++;
      }

      // 长度/面积类型统计（简单分类，可根据实际需求调整）
      const lengthArea = waterSystem.lengthArea || '';
      if (
        lengthArea.includes('河') ||
        lengthArea.includes('江') ||
        lengthArea.includes('川')
      ) {
        lengthAreaCategories[0].count++; // 河流
      } else if (lengthArea.includes('湖') || lengthArea.includes('海')) {
        lengthAreaCategories[1].count++; // 湖泊
      } else if (lengthArea.includes('库') || lengthArea.includes('坝')) {
        lengthAreaCategories[2].count++; // 水库
      } else {
        lengthAreaCategories[3].count++; // 其他
      }
    });

    const byRegion = Array.from(regionMap.values()).sort(
      (a, b) => b.count - a.count
    );
    const byLengthArea = lengthAreaCategories
      .filter(category => category.count > 0)
      .sort((a, b) => b.count - a.count);

    return {
      total,
      byRegion,
      byLengthArea,
    };
  }

  /**
   * 根据长度/面积范围查询
   */
  async findByLengthArea(lengthAreaKeyword?: string): Promise<WaterSystem[]> {
    const whereConditions: any = {};

    if (lengthAreaKeyword) {
      whereConditions.lengthArea = {
        [Symbol.for('like')]: `%${lengthAreaKeyword}%`,
      };
    }

    const result = await this.findAll({
      query: whereConditions,
      include: [
        {
          model: RegionDict,
          as: 'regionDict',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });
    return result.list;
  }

  /**
   * 批量导入水系数据
   */
  async batchImportWaterSystems(
    waterSystems: CreateWaterSystemDTO[]
  ): Promise<void> {
    // 批量验证数据
    for (const waterSystem of waterSystems) {
      await this.validateWaterSystemData(waterSystem);
    }

    await this.batchCreate(waterSystems as any);
  }

  /**
   * 验证水系数据
   */
  private async validateWaterSystemData(
    data: CreateWaterSystemDTO | UpdateWaterSystemDTO,
    excludeId?: number
  ): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('水系名称不能为空');
    }

    if (!data.code || data.code.trim().length === 0) {
      throw new Error('水系编号不能为空');
    }

    // 验证编号唯一性
    const existingWaterSystem = await this.findByCode(data.code.trim());
    if (existingWaterSystem && (!excludeId || existingWaterSystem.id !== excludeId)) {
      throw new Error(`编号 "${data.code}" 已存在，请使用其他编号`);
    }

    if (data.longitude) {
      // 验证经纬度范围
      if (data.longitude < -180 || data.longitude > 180) {
        throw new Error('经度范围应在-180到180之间');
      }
    }

    if (data.latitude) {
      // 验证经纬度范围
      if (data.latitude < -90 || data.latitude > 90) {
        throw new Error('纬度范围应在-90到90之间');
      }
    }

    // 验证长度/面积格式
    if (data.lengthArea && typeof data.lengthArea === 'string') {
      if (data.lengthArea.trim().length === 0) {
        throw new Error('长度/面积不能为空字符串');
      }
    }
  }
}
